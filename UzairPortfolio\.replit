modules = ["nodejs-20", "python-3.11"]

[nix]
channel = "stable-24_05"

[workflows]
runButton = "Project"

[[workflows.workflow]]
name = "Project"
mode = "parallel"
author = "agent"

[[workflows.workflow.tasks]]
task = "workflow.run"
args = "Portfolio Server"

[[workflows.workflow]]
name = "Portfolio Server"
author = "agent"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "python -m http.server 5000"
waitForPort = 5000

[deployment]
run = ["sh", "-c", "python -m http.server 5000"]

[[ports]]
localPort = 5000
externalPort = 80

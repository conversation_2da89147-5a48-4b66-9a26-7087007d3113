/* CSS Custom Properties for Theme Management */
:root {
    /* Light Theme Colors */
    --primary-color: #2C2C2C;
    --secondary-color: #F8F8F8;
    --accent-color: #D4AF37;
    --text-color: #333333;
    --background-color: #FFFFFF;
    --hover-color: #1A1A1A;
    --border-color: #E0E0E0;
    --shadow-color: rgba(44, 44, 44, 0.1);
    
    /* Typography */
    --font-heading: 'Playfair Display', serif;
    --font-body: 'Inter', sans-serif;
    
    /* Spacing */
    --section-padding: 80px 0;
    --container-padding: 0 20px;
    --border-radius: 8px;
    
    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* Z-index */
    --z-navbar: 1000;
    --z-lightbox: 2000;
}

/* Dark Theme Colors */
[data-theme="dark"] {
    --primary-color: #F8F8F8;
    --secondary-color: #2C2C2C;
    --accent-color: #D4AF37;
    --text-color: #E0E0E0;
    --background-color: #1A1A1A;
    --hover-color: #F0F0F0;
    --border-color: #404040;
    --shadow-color: rgba(248, 248, 248, 0.1);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-body);
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
    transition: all var(--transition-normal);
}

img {
    max-width: 100%;
    height: auto;
    display: block;
}

a {
    text-decoration: none;
    color: inherit;
    transition: all var(--transition-fast);
}

ul {
    list-style: none;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--container-padding);
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: var(--background-color);
    border-bottom: 1px solid var(--border-color);
    z-index: var(--z-navbar);
    transition: all var(--transition-normal);
    backdrop-filter: blur(10px);
}

.navbar.scrolled {
    box-shadow: 0 2px 20px var(--shadow-color);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo a {
    font-family: var(--font-heading);
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
}

.nav-menu {
    display: flex;
    gap: 2rem;
}

.nav-link {
    color: var(--text-color);
    font-weight: 500;
    position: relative;
    padding: 0.5rem 0;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--accent-color);
    transition: width var(--transition-fast);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

/* Theme Toggle */
.theme-toggle {
    position: relative;
    width: 50px;
    height: 25px;
    background-color: var(--secondary-color);
    border-radius: 25px;
    border: 2px solid var(--border-color);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 5px;
    transition: all var(--transition-normal);
}

.theme-toggle::before {
    content: '';
    position: absolute;
    width: 19px;
    height: 19px;
    background-color: var(--accent-color);
    border-radius: 50%;
    left: 2px;
    transition: transform var(--transition-normal);
}

[data-theme="dark"] .theme-toggle::before {
    transform: translateX(23px);
}

.theme-toggle i {
    font-size: 0.8rem;
    z-index: 2;
}

.sun-icon {
    color: #FFA500;
}

.moon-icon {
    color: #4169E1;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.mobile-menu-toggle .bar {
    width: 25px;
    height: 3px;
    background-color: var(--primary-color);
    transition: all var(--transition-fast);
}

.mobile-menu-toggle.active .bar:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.mobile-menu-toggle.active .bar:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active .bar:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

/* Hero Section */
.hero {
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    position: relative;
    background: linear-gradient(135deg, var(--background-color) 0%, var(--secondary-color) 100%);
}

.hero-content {
    max-width: 800px;
    padding: 0 20px;
    animation: fadeInUp 1s ease-out;
}

.hero-title {
    font-family: var(--font-heading);
    font-size: clamp(3rem, 8vw, 6rem);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 1rem;
    letter-spacing: -0.02em;
}

.hero-tagline {
    font-size: clamp(1.2rem, 3vw, 1.8rem);
    color: var(--text-color);
    margin-bottom: 3rem;
    font-weight: 300;
    letter-spacing: 0.5px;
}

.hero-cta {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.hero-scroll {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
}

.scroll-indicator {
    display: inline-block;
    color: var(--text-color);
    font-size: 1.5rem;
    animation: bounce 2s infinite;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 12px 32px;
    border-radius: var(--border-radius);
    font-weight: 500;
    font-size: 1rem;
    text-align: center;
    transition: all var(--transition-fast);
    cursor: pointer;
    border: 2px solid transparent;
}

.btn-primary {
    background-color: var(--accent-color);
    color: #FFFFFF;
}

.btn-primary:hover {
    background-color: #B8941F;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.3);
}

.btn-secondary {
    background-color: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-secondary:hover {
    background-color: var(--primary-color);
    color: var(--background-color);
}

/* Section Styles */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-family: var(--font-heading);
    font-size: clamp(2.5rem, 5vw, 3.5rem);
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-weight: 600;
}

.section-divider {
    width: 60px;
    height: 4px;
    background-color: var(--accent-color);
    margin: 0 auto 1.5rem;
    border-radius: 2px;
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--text-color);
    max-width: 600px;
    margin: 0 auto;
    opacity: 0.8;
}

/* About Section */
.about {
    padding: var(--section-padding);
}

.about-content {
    max-width: 800px;
    margin: 0 auto;
}

.about-text {
    text-align: center;
}

.about-intro {
    font-size: 1.3rem;
    font-weight: 500;
    color: var(--primary-color);
    margin-bottom: 2rem;
    line-height: 1.5;
}

.about-text p {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    opacity: 0.9;
}

.about-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-top: 3rem;
    flex-wrap: wrap;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-family: var(--font-heading);
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--accent-color);
    line-height: 1;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-color);
    opacity: 0.8;
    margin-top: 0.5rem;
}

/* Gallery Section */
.gallery {
    padding: var(--section-padding);
    background-color: var(--secondary-color);
}

.gallery-filters {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 10px 24px;
    background-color: transparent;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-color);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    font-family: var(--font-body);
    font-size: 0.95rem;
}

.filter-btn:hover,
.filter-btn.active {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
    color: #FFFFFF;
    transform: translateY(-2px);
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
    align-items: start; /* Align items to start to handle different heights */
}

.gallery-item {
    position: relative;
    border-radius: var(--border-radius);
    overflow: hidden;
    cursor: pointer;
    transition: all var(--transition-normal);
    background-color: var(--background-color);
    box-shadow: 0 4px 20px var(--shadow-color);
}

.gallery-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px var(--shadow-color);
}

.gallery-item img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: all var(--transition-normal);
}

/* 3:4 aspect ratio for portrait, abstract, street, and nature categories */
.gallery-item[data-category="portrait"] img,
.gallery-item[data-category="abstract"] img,
.gallery-item[data-category="street"] img,
.gallery-item[data-category="nature"] img {
    height: 400px; /* 3:4 ratio fallback */
    aspect-ratio: 3/4;
    object-fit: cover;
    object-position: center;
}

/* Landscape images keep their original wider aspect ratio */
.gallery-item[data-category="landscape"] img {
    height: 225px; /* Wider aspect ratio for landscape fallback */
    aspect-ratio: 4/3;
    object-fit: cover;
    object-position: center;
}

/* Fallback for browsers that don't support aspect-ratio */
@supports not (aspect-ratio: 3/4) {
    .gallery-item[data-category="portrait"] img,
    .gallery-item[data-category="abstract"] img,
    .gallery-item[data-category="street"] img,
    .gallery-item[data-category="nature"] img {
        height: 400px; /* Fixed height for 3:4 ratio */
    }

    .gallery-item[data-category="landscape"] img {
        height: 225px; /* Fixed height for landscape */
    }
}

.gallery-item:hover img {
    transform: scale(1.05);
}

.gallery-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: 2rem 1.5rem 1.5rem;
    transform: translateY(100%);
    transition: all var(--transition-normal);
}

.gallery-item:hover .gallery-overlay {
    transform: translateY(0);
}

.gallery-overlay h3 {
    font-family: var(--font-heading);
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.gallery-overlay p {
    font-size: 0.95rem;
    opacity: 0.9;
}

.gallery-item.hidden {
    display: none;
}

/* Contact Section */
.contact {
    padding: var(--section-padding);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    max-width: 900px;
    margin: 0 auto;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2.5rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
}

.contact-icon {
    width: 50px;
    height: 50px;
    background-color: var(--accent-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.contact-icon i {
    color: white;
    font-size: 1.2rem;
}

.contact-details h3 {
    font-family: var(--font-heading);
    font-size: 1.3rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.contact-details a {
    color: var(--accent-color);
    font-weight: 500;
}

.contact-details a:hover {
    text-decoration: underline;
}

.contact-details p {
    color: var(--text-color);
    opacity: 0.8;
}

.contact-cta {
    background-color: var(--secondary-color);
    padding: 2.5rem;
    border-radius: var(--border-radius);
    text-align: center;
}

.contact-cta h3 {
    font-family: var(--font-heading);
    font-size: 1.8rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-weight: 600;
}

.contact-cta p {
    color: var(--text-color);
    margin-bottom: 2rem;
    opacity: 0.8;
    line-height: 1.6;
}

/* Footer */
.footer {
    background-color: var(--primary-color);
    color: var(--background-color);
    padding: 2rem 0;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-text {
    opacity: 0.8;
}

.footer-social {
    display: flex;
    gap: 1rem;
}

.footer-social a {
    width: 40px;
    height: 40px;
    background-color: var(--accent-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    transition: all var(--transition-fast);
}

.footer-social a:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
}

/* Lightbox */
.lightbox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    z-index: var(--z-lightbox);
    display: none;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.lightbox.active {
    display: flex;
}

.lightbox-content {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
    background-color: var(--background-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.lightbox-close {
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 2rem;
    color: white;
    cursor: pointer;
    z-index: 10;
    background-color: rgba(0, 0, 0, 0.5);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
}

.lightbox-close:hover {
    background-color: rgba(0, 0, 0, 0.8);
}

.lightbox img {
    width: 100%;
    height: auto;
    max-height: 70vh;
    object-fit: contain;
}

.lightbox-info {
    padding: 1.5rem;
    background-color: var(--background-color);
}

.lightbox-info h3 {
    font-family: var(--font-heading);
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.lightbox-info p {
    color: var(--text-color);
    opacity: 0.8;
}

.lightbox-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 1rem;
    pointer-events: none;
}

.lightbox-prev,
.lightbox-next {
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.2rem;
    transition: all var(--transition-fast);
    pointer-events: all;
}

.lightbox-prev:hover,
.lightbox-next:hover {
    background-color: rgba(0, 0, 0, 0.8);
    transform: scale(1.1);
}

/* Enhanced Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0) translateX(-50%);
    }
    40% {
        transform: translateY(-10px) translateX(-50%);
    }
    60% {
        transform: translateY(-5px) translateX(-50%);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

.fade-in-element {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.fade-in-element.animate-in {
    opacity: 1;
    transform: translateY(0);
}

.stat-number {
    animation: scaleIn 0.8s ease-out;
}

.gallery-item {
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.hero-title {
    animation: fadeInUp 1.2s ease-out;
}

.hero-tagline {
    animation: fadeInUp 1.2s ease-out 0.2s both;
}

.hero-cta {
    animation: fadeInUp 1.2s ease-out 0.4s both;
}

/* Enhanced button animations */
.btn {
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

/* Enhanced gallery animations */
.gallery-item {
    transform: translateY(20px);
    opacity: 0;
    animation: slideInUp 0.6s ease-out forwards;
}

.gallery-item:nth-child(1) { animation-delay: 0.1s; }
.gallery-item:nth-child(2) { animation-delay: 0.2s; }
.gallery-item:nth-child(3) { animation-delay: 0.3s; }
.gallery-item:nth-child(4) { animation-delay: 0.4s; }
.gallery-item:nth-child(5) { animation-delay: 0.5s; }
.gallery-item:nth-child(6) { animation-delay: 0.6s; }

/* Loading animation for images */
.gallery-item img {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.gallery-item img.loaded {
    opacity: 1;
}

/* Enhanced contact section animations */
.contact-item {
    transform: translateX(-30px);
    opacity: 0;
    animation: fadeInLeft 0.8s ease-out forwards;
}

.contact-item:nth-child(1) { animation-delay: 0.1s; }
.contact-item:nth-child(2) { animation-delay: 0.2s; }
.contact-item:nth-child(3) { animation-delay: 0.3s; }
.contact-item:nth-child(4) { animation-delay: 0.4s; }
.contact-item:nth-child(5) { animation-delay: 0.5s; }

.contact-cta {
    transform: translateX(30px);
    opacity: 0;
    animation: fadeInRight 0.8s ease-out 0.3s forwards;
}

/* Stats animation */
.stat {
    transform: scale(0.8);
    opacity: 0;
    animation: scaleIn 0.6s ease-out forwards;
}

.stat:nth-child(1) { animation-delay: 0.2s; }
.stat:nth-child(2) { animation-delay: 0.4s; }
.stat:nth-child(3) { animation-delay: 0.6s; }

/* Theme toggle animation enhancement */
.theme-toggle:hover {
    transform: scale(1.1);
}

/* Enhanced navbar animation */
.nav-link {
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.1), transparent);
    transition: left 0.3s;
}

.nav-link:hover::before {
    left: 100%;
}

/* Page transitions */
body {
    opacity: 0;
    animation: fadeIn 0.5s ease-out forwards;
}

@keyframes fadeIn {
    to { opacity: 1; }
}

/* Enhanced filter button animations */
.filter-btn {
    position: relative;
    overflow: hidden;
}

.filter-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(212, 175, 55, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
}

.filter-btn:hover::after {
    width: 100%;
    height: 100%;
}

/* Scroll indicator animation */
.scroll-indicator {
    transition: all 0.3s ease;
}

.scroll-indicator:hover {
    transform: translateY(-5px);
    color: var(--accent-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    :root {
        --section-padding: 60px 0;
    }
    
    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background-color: var(--background-color);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 2rem;
        gap: 1.5rem;
        transition: left var(--transition-normal);
        border-top: 1px solid var(--border-color);
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .mobile-menu-toggle {
        display: flex;
    }
    
    .theme-toggle {
        order: -1;
        margin-right: 1rem;
    }
    
    .hero-cta {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }
    
    .btn {
        min-width: 200px;
    }
    
    .about-stats {
        gap: 2rem;
    }
    
    .gallery-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .contact-content {
        grid-template-columns: 1fr;
        gap: 2.5rem;
    }
    
    .contact-cta {
        padding: 2rem;
    }
    
    .footer-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .lightbox-content {
        margin: 1rem;
    }
    
    .lightbox-nav {
        padding: 0 0.5rem;
    }
    
    .lightbox-prev,
    .lightbox-next {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
}

/* Work Page Specific Styles */
.work-hero {
    padding: 120px 0 80px;
    background: linear-gradient(135deg, var(--background-color) 0%, var(--secondary-color) 100%);
    text-align: center;
}

.work-hero-content {
    max-width: 800px;
    margin: 0 auto;
    animation: fadeInUp 1s ease-out;
}

.work-hero-title {
    font-family: var(--font-heading);
    font-size: clamp(2.5rem, 6vw, 4rem);
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-weight: 600;
}

.work-hero-subtitle {
    font-size: 1.3rem;
    color: var(--text-color);
    opacity: 0.8;
    max-width: 600px;
    margin: 0 auto;
}

/* Awards Section */
.awards-section {
    padding: var(--section-padding);
}

.awards-grid {
    display: grid;
    gap: 2rem;
    max-width: 800px;
    margin: 0 auto;
}

.award-card {
    background-color: var(--background-color);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 2.5rem;
    display: flex;
    align-items: flex-start;
    gap: 2rem;
    box-shadow: 0 4px 20px var(--shadow-color);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.award-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px var(--shadow-color);
    border-color: var(--accent-color);
}

.award-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--accent-color), #B8941F);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    animation: pulse 2s infinite;
}

.award-icon i {
    color: white;
    font-size: 2rem;
}

.award-content h3 {
    font-family: var(--font-heading);
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-weight: 600;
}

.award-content p {
    color: var(--text-color);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.award-stats {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.stat-item {
    font-size: 0.9rem;
    color: var(--accent-color);
    font-weight: 500;
}

/* Experience Section */
.experience-section {
    padding: var(--section-padding);
    background-color: var(--secondary-color);
}

.timeline {
    max-width: 900px;
    margin: 0 auto;
    position: relative;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 30px;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(to bottom, var(--accent-color), transparent);
}

.timeline-item {
    position: relative;
    padding-left: 80px;
    margin-bottom: 3rem;
    opacity: 0;
    transform: translateX(-30px);
    transition: all 0.8s ease;
}

.timeline-item.timeline-animate {
    opacity: 1;
    transform: translateX(0);
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: 19px;
    top: 10px;
    width: 22px;
    height: 22px;
    background-color: var(--accent-color);
    border: 4px solid var(--background-color);
    border-radius: 50%;
    box-shadow: 0 0 0 3px var(--accent-color);
}

.timeline-date {
    display: inline-block;
    background-color: var(--accent-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 1rem;
}

.timeline-content {
    background-color: var(--background-color);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: 0 4px 20px var(--shadow-color);
    border-left: 4px solid var(--accent-color);
}

.timeline-content h3 {
    font-family: var(--font-heading);
    font-size: 1.4rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.timeline-content h4 {
    color: var(--accent-color);
    font-size: 1.1rem;
    margin-bottom: 1rem;
    font-weight: 500;
}

.timeline-content ul {
    margin-bottom: 1.5rem;
}

.timeline-content li {
    color: var(--text-color);
    margin-bottom: 0.5rem;
    padding-left: 1rem;
    position: relative;
}

.timeline-content li::before {
    content: '•';
    color: var(--accent-color);
    position: absolute;
    left: 0;
    font-weight: bold;
}

.experience-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tag {
    background-color: var(--secondary-color);
    color: var(--text-color);
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    border: 1px solid var(--border-color);
}

/* Skills Section */
.skills-section {
    padding: var(--section-padding);
}

.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 3rem;
    max-width: 1000px;
    margin: 0 auto;
}

.skill-category {
    background-color: var(--background-color);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: 0 4px 20px var(--shadow-color);
    border: 2px solid var(--border-color);
    transition: all 0.4s ease;
}

.skill-category:hover {
    transform: translateY(-5px);
    border-color: var(--accent-color);
}

.skill-category h3 {
    font-family: var(--font-heading);
    font-size: 1.3rem;
    color: var(--primary-color);
    margin-bottom: 2rem;
    text-align: center;
    font-weight: 600;
}

.skills-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.skill-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.skill-name {
    font-weight: 500;
    color: var(--text-color);
    font-size: 0.95rem;
}

.skill-bar {
    height: 8px;
    background-color: var(--secondary-color);
    border-radius: 4px;
    overflow: hidden;
}

.skill-progress {
    height: 100%;
    background: linear-gradient(90deg, var(--accent-color), #B8941F);
    border-radius: 4px;
    width: 0;
    transition: width 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Education Section */
.education-section {
    padding: var(--section-padding);
    background-color: var(--secondary-color);
}

.education-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 1000px;
    margin: 0 auto;
}

.education-card {
    background-color: var(--background-color);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: 0 4px 20px var(--shadow-color);
    border: 2px solid var(--border-color);
    transition: all 0.4s ease;
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
}

.education-card:hover {
    transform: translateY(-5px);
    border-color: var(--accent-color);
}

.education-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--accent-color), #B8941F);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.education-icon i {
    color: white;
    font-size: 1.5rem;
}

.education-content h3 {
    font-family: var(--font-heading);
    font-size: 1.2rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.education-content h4 {
    color: var(--accent-color);
    font-size: 1rem;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.education-date {
    color: var(--text-color);
    font-size: 0.9rem;
    opacity: 0.8;
    display: block;
    margin-bottom: 1rem;
}

.education-content p {
    color: var(--text-color);
    line-height: 1.5;
}

/* Social Section */
.social-section {
    padding: var(--section-padding);
}

.social-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 800px;
    margin: 0 auto;
}

.social-card {
    background-color: var(--background-color);
    padding: 2.5rem;
    border-radius: var(--border-radius);
    box-shadow: 0 4px 20px var(--shadow-color);
    border: 2px solid var(--border-color);
    text-align: center;
    transition: all 0.4s ease;
}

.social-card:hover {
    transform: translateY(-8px);
    border-color: var(--accent-color);
}

.social-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--accent-color), #B8941F);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.social-icon i {
    color: white;
    font-size: 2rem;
}

.social-content h3 {
    font-family: var(--font-heading);
    font-size: 1.3rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.social-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--accent-color);
    margin-bottom: 1rem;
}

.social-content p {
    color: var(--text-color);
    margin-bottom: 2rem;
    line-height: 1.5;
}

/* Responsive Design for Work Page */
@media (max-width: 768px) {
    .work-hero {
        padding: 100px 0 60px;
    }
    
    .award-card,
    .education-card {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
    
    .timeline::before {
        left: 15px;
    }
    
    .timeline-item {
        padding-left: 50px;
    }
    
    .timeline-item::before {
        left: 4px;
    }
    
    .skills-grid,
    .education-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .timeline-content {
        padding: 1.5rem;
    }
}

/* Gallery Page Specific Styles */
.gallery-hero {
    padding: 120px 0 80px;
    background: linear-gradient(135deg, var(--background-color) 0%, var(--secondary-color) 100%);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.gallery-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="camera" patternUnits="userSpaceOnUse" width="100" height="100"><circle cx="50" cy="50" r="2" fill="rgba(212,175,55,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23camera)"/></svg>');
    pointer-events: none;
}

.gallery-hero-content {
    max-width: 800px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
    animation: fadeInUp 1s ease-out;
}

.gallery-hero-title {
    font-family: var(--font-heading);
    font-size: clamp(2.5rem, 6vw, 4rem);
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-weight: 600;
}

.gallery-hero-subtitle {
    font-size: 1.3rem;
    color: var(--text-color);
    opacity: 0.8;
    max-width: 600px;
    margin: 0 auto;
}

/* Enhanced Gallery Main Section */
.gallery-main {
    padding: 80px 0;
    background-color: var(--background-color);
}

.gallery-filters {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 4rem;
    flex-wrap: wrap;
}

.filter-btn {
    position: relative;
    padding: 12px 30px;
    background-color: transparent;
    border: 2px solid var(--border-color);
    border-radius: 50px;
    color: var(--text-color);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    font-family: var(--font-body);
    font-size: 1rem;
    overflow: hidden;
}

.filter-btn:hover,
.filter-btn.active {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
    color: #FFFFFF;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.3);
}

.ripple {
    position: absolute;
    border-radius: 50%;
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    background-color: rgba(255, 255, 255, 0.3);
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Enhanced Gallery Grid */
.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
    align-items: start; /* Align items to start to handle different heights */
}

.gallery-item-animated {
    opacity: 0;
    transform: translateY(30px) scale(0.9);
    transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.gallery-item-visible {
    opacity: 1;
    transform: translateY(0) scale(1);
}

.gallery-item-show {
    animation: slideInScale 0.6s ease-out forwards;
}

.gallery-item-hidden {
    animation: slideOutScale 0.3s ease-in forwards;
}

@keyframes slideInScale {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.8);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes slideOutScale {
    from {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    to {
        opacity: 0;
        transform: translateY(-20px) scale(0.8);
    }
}

.gallery-item {
    position: relative;
    border-radius: var(--border-radius);
    overflow: hidden;
    cursor: pointer;
    background-color: var(--background-color);
    box-shadow: 0 6px 30px var(--shadow-color);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.gallery-item:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 60px var(--shadow-color);
}

.gallery-item img {
    width: 100%;
    height: 280px;
    object-fit: cover;
    transition: all 0.4s ease;
    opacity: 0;
}

/* 3:4 aspect ratio for portrait, abstract, street, and nature categories */
.gallery-item[data-category="portrait"] img,
.gallery-item[data-category="abstract"] img,
.gallery-item[data-category="street"] img,
.gallery-item[data-category="nature"] img {
    height: 400px; /* 3:4 ratio for 300px width containers */
    aspect-ratio: 3/4;
    object-fit: cover;
    object-position: center;
}

/* Landscape images keep their original wider aspect ratio */
.gallery-item[data-category="landscape"] img {
    height: 225px; /* Wider aspect ratio for landscape */
    aspect-ratio: 4/3;
    object-fit: cover;
    object-position: center;
}

.gallery-item.image-loaded img {
    opacity: 1;
}

.gallery-item:hover img {
    transform: scale(1.1);
}

.gallery-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.9));
    color: white;
    padding: 3rem 2rem 2rem;
    transform: translateY(100%);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.gallery-item:hover .gallery-overlay {
    transform: translateY(0);
}

.gallery-overlay h3 {
    font-family: var(--font-heading);
    font-size: 1.4rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.gallery-overlay p {
    font-size: 1rem;
    opacity: 0.9;
}

/* Gallery Stats Section */
.gallery-stats-section {
    padding: 80px 0;
    background-color: var(--secondary-color);
}

.gallery-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    max-width: 800px;
    margin: 0 auto;
}

.gallery-stat-card {
    background-color: var(--background-color);
    padding: 2.5rem 2rem;
    border-radius: var(--border-radius);
    text-align: center;
    box-shadow: 0 6px 30px var(--shadow-color);
    border: 2px solid var(--border-color);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.gallery-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--accent-color), #B8941F);
    transition: left 0.5s ease;
}

.gallery-stat-card:hover::before {
    left: 0;
}

.gallery-stat-card:hover {
    transform: translateY(-8px);
    border-color: var(--accent-color);
    box-shadow: 0 15px 50px var(--shadow-color);
}

.stat-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--accent-color), #B8941F);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    transition: all 0.3s ease;
}

.gallery-stat-card:hover .stat-icon {
    transform: scale(1.1) rotate(5deg);
}

.stat-icon i {
    color: white;
    font-size: 1.8rem;
}

.stat-number {
    font-family: var(--font-heading);
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--accent-color);
    margin-bottom: 0.5rem;
    display: block;
}

.stat-label {
    color: var(--text-color);
    font-size: 1rem;
    font-weight: 500;
    opacity: 0.8;
}

/* Responsive Design for Gallery Page */
@media (max-width: 768px) {
    .gallery-hero {
        padding: 100px 0 60px;
    }
    
    .gallery-filters {
        gap: 0.5rem;
        margin-bottom: 2.5rem;
    }
    
    .filter-btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
    
    .gallery-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .gallery-item img {
        height: 250px;
    }

    /* Maintain 3:4 ratio on mobile for specific categories */
    .gallery-item[data-category="portrait"] img,
    .gallery-item[data-category="abstract"] img,
    .gallery-item[data-category="street"] img,
    .gallery-item[data-category="nature"] img {
        height: 350px; /* 3:4 ratio for mobile */
        aspect-ratio: 3/4;
    }

    /* Landscape images on mobile */
    .gallery-item[data-category="landscape"] img {
        height: 200px; /* Wider aspect ratio for landscape on mobile */
        aspect-ratio: 4/3;
    }
    
    .gallery-stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
    
    .gallery-stat-card {
        padding: 2rem 1.5rem;
    }
}

@media (max-width: 480px) {
    .gallery-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .gallery-overlay {
        padding: 2rem 1.5rem 1.5rem;
    }
    
    .gallery-overlay h3 {
        font-size: 1.2rem;
    }
}

@media (max-width: 480px) {
    .nav-container {
        padding: 0 15px;
    }
    
    .container {
        padding: 0 15px;
    }
    
    .hero-content {
        padding: 0 15px;
    }
    
    .gallery-filters {
        gap: 0.5rem;
    }
    
    .filter-btn {
        padding: 8px 16px;
        font-size: 0.9rem;
    }
    
    .about-stats {
        flex-direction: column;
        gap: 1.5rem;
    }
    
    .contact-item {
        gap: 1rem;
    }
    
    .contact-icon {
        width: 40px;
        height: 40px;
    }
    
    .contact-icon i {
        font-size: 1rem;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --shadow-color: rgba(0, 0, 0, 0.3);
    }
    
    [data-theme="dark"] {
        --shadow-color: rgba(255, 255, 255, 0.3);
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    html {
        scroll-behavior: auto;
    }
}

/* Print styles */
@media print {
    .navbar,
    .hero-scroll,
    .theme-toggle,
    .mobile-menu-toggle,
    .lightbox {
        display: none !important;
    }
    
    .hero {
        height: auto;
        padding: 2rem 0;
    }
    
    body {
        background-color: white !important;
        color: black !important;
    }
}
